/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { release } from 'os';
import { localize } from '../../../../nls.js';

// Menu translations
export const titlebarMessages = {
	login: localize('joycode.setting.menu.login', 'Login'),
	checkUpdate: localize('joycode.setting.menu.checkUpdate', 'Check for Updates...'),
	projectSettings: localize('joycode.setting.menu.projectSettings', 'Project Settings'),
	settings: localize('joycode.setting.menu.settings', 'Settings...'),
	joycodeSettings: localize('joycode.setting.menu.joycodeSettings', 'JoyCode Settings'),
	theme: localize('joycode.setting.menu.theme', 'Theme'),
	language: localize('joycode.setting.menu.language', 'Language'),
	keybindings: localize('joycode.setting.menu.keybindings', 'Keyboard Shortcuts'),
	extensions: localize('joycode.setting.menu.extensions', 'Extensions'),
	about: localize('joycode.setting.menu.about', 'About'),
	releaseNote: localize('joycode.setting.menu.releaseNote', 'Release Notes'),
	logout: localize('joycode.setting.menu.logout', 'Logout')
};

// Language switch related translations
export const changeDisplayLanguageMessages = {
	confirm: localize('joycode.setting.language.confirm', 'Do you want to change display language to {0}? JoyCode needs to restart to apply changes.'),
	yes: localize('joycode.setting.language.yes', 'Yes'),
	error: localize('joycode.setting.language.error', 'Error occurred while changing display language: {0}')
};




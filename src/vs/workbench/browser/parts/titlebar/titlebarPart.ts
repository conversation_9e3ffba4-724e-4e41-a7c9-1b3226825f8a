/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import './media/titlebarpart.css';
import * as DOM from '../../../../base/browser/dom.js';
import { localize, localize2 } from '../../../../nls.js';
import { MultiWindowParts, Part } from '../../part.js';
import { ITitleService } from '../../../services/title/browser/titleService.js';
import { ILocaleService } from '../../../services/localization/common/locale.js';
import { INotificationService, Severity } from '../../../../platform/notification/common/notification.js';
import { getWCOTitlebarAreaRect, getZoomFactor, isWCOEnabled } from '../../../../base/browser/browser.js';
import { MenuBarVisibility, getTitleBarStyle, getMenuBarVisibility, hasCustomTitlebar, hasNativeTitlebar, DEFAULT_CUSTOM_TITLEBAR_HEIGHT } from '../../../../platform/window/common/window.js';
import { IContextMenuService } from '../../../../platform/contextview/browser/contextView.js';
import { StandardMouseEvent } from '../../../../base/browser/mouseEvent.js';
import { IConfigurationService, IConfigurationChangeEvent } from '../../../../platform/configuration/common/configuration.js';
import { DisposableStore, IDisposable } from '../../../../base/common/lifecycle.js';
import { IBrowserWorkbenchEnvironmentService } from '../../../services/environment/browser/environmentService.js';
import { IThemeService } from '../../../../platform/theme/common/themeService.js';
import { TITLE_BAR_ACTIVE_BACKGROUND, TITLE_BAR_ACTIVE_FOREGROUND, TITLE_BAR_INACTIVE_FOREGROUND, TITLE_BAR_INACTIVE_BACKGROUND, TITLE_BAR_BORDER, WORKBENCH_BACKGROUND } from '../../../common/theme.js';
import { isMacintosh, isWindows, isLinux, isWeb, isNative, language, platformLocale } from '../../../../base/common/platform.js';
import { getNLSLanguage } from '../../../../nls.js';
import { Color } from '../../../../base/common/color.js';
import { EventType, EventHelper, Dimension, append, $, addDisposableListener, prepend, reset, getWindow, getWindowId, isAncestor, getActiveDocument, isHTMLElement } from '../../../../base/browser/dom.js';
import { CustomMenubarControl } from './menubarControl.js';
import { IInstantiationService, ServicesAccessor } from '../../../../platform/instantiation/common/instantiation.js';
import { Emitter, Event } from '../../../../base/common/event.js';
import { IStorageService, StorageScope } from '../../../../platform/storage/common/storage.js';
import { Parts, IWorkbenchLayoutService, ActivityBarPosition, LayoutSettings, EditorActionsLocation, EditorTabsMode } from '../../../services/layout/browser/layoutService.js';
import { createActionViewItem, fillInActionBarActions as fillInActionBarActions } from '../../../../platform/actions/browser/menuEntryActionViewItem.js';
import { Action2, IMenu, IMenuService, MenuId, registerAction2 } from '../../../../platform/actions/common/actions.js';
import { IContextKeyService, RawContextKey } from '../../../../platform/contextkey/common/contextkey.js';
import { IHostService } from '../../../services/host/browser/host.js';
import { WindowTitle } from './windowTitle.js';
import { CommandCenterControl } from './commandCenterControl.js';
import { Categories } from '../../../../platform/action/common/actionCommonCategories.js';
import { WorkbenchToolBar } from '../../../../platform/actions/browser/toolbar.js';
import { ACCOUNTS_ACTIVITY_ID, GLOBAL_ACTIVITY_ID } from '../../../common/activity.js';
import { AccountsActivityActionViewItem, isAccountsActionVisible, SimpleAccountActivityActionViewItem, SimpleGlobalActivityActionViewItem } from '../globalCompositeBar.js';
import { HoverPosition } from '../../../../base/browser/ui/hover/hoverWidget.js';
import { IEditorGroupsContainer, IEditorGroupsService } from '../../../services/editor/common/editorGroupsService.js';
import { ActionRunner, IAction } from '../../../../base/common/actions.js';
import { IEditorService } from '../../../services/editor/common/editorService.js';
import { ActionsOrientation, IActionViewItem, prepareActions } from '../../../../base/browser/ui/actionbar/actionbar.js';
import { EDITOR_CORE_NAVIGATION_COMMANDS } from '../editor/editorCommands.js';
import { AnchorAlignment } from '../../../../base/browser/ui/contextview/contextview.js';
import { EditorPane } from '../editor/editorPane.js';
import { IKeybindingService } from '../../../../platform/keybinding/common/keybinding.js';
import { ResolvedKeybinding } from '../../../../base/common/keybindings.js';
import { EditorCommandsContextActionRunner } from '../editor/editorTabsControl.js';
import { IEditorCommandsContext, IEditorPartOptionsChangeEvent, IToolbarActions } from '../../../common/editor.js';
import { CodeWindow, mainWindow } from '../../../../base/browser/window.js';
import { ACCOUNTS_ACTIVITY_TILE_ACTION, GLOBAL_ACTIVITY_TITLE_ACTION } from './titlebarActions.js';
import { IView } from '../../../../base/browser/ui/grid/grid.js';
import { createInstantHoverDelegate } from '../../../../base/browser/ui/hover/hoverDelegateFactory.js';
import { IBaseActionViewItemOptions } from '../../../../base/browser/ui/actionbar/actionViewItems.js';
import { IHoverDelegate } from '../../../../base/browser/ui/hover/hoverDelegate.js';
import { CommandsRegistry } from '../../../../platform/commands/common/commands.js';
import { safeIntl } from '../../../../base/common/date.js';
import { TitleBarVisibleContext } from '../../../common/contextkeys.js';
import { IUpdateService, StateType } from '../../../../platform/update/common/update.js';
import { ICommandService } from '../../../../platform/commands/common/commands.js';
import { ILoginService } from '../../../../platform/login/common/login.js';
import { IUserDataSyncAccountService, IUserDataSyncAccount } from '../../../../platform/userDataSync/common/userDataSyncAccount.js';

// Menu text interface that satisfies ContextKeyValue constraint
interface IMenuTexts {
	[key: string]: string;
	logout: string;
	theme: string;
	language: string;
	keybindings: string;
	settings: string;
	joycodeSettings: string;
	extensions: string;
	releaseNote: string;
	about: string;
	checkUpdate: string;
	error: string;
	login: string;
}

// Context key for menu texts
export const MenuTextsContextKey = new RawContextKey<IMenuTexts>('joycode.l10n.texts', undefined);

// Extend the IUserDataSyncAccount interface
interface ExtendedIUserDataSyncAccount extends IUserDataSyncAccount {
	avatar?: string;
	displayName?: string;
}

// Extend the IUserDataSyncAccountService interface
interface ExtendedIUserDataSyncAccountService extends IUserDataSyncAccountService {
	login(): Promise<void>;
	logout(): Promise<void>;
}

// Context key for menu texts
export const MenuTextsContextKey = new RawContextKey<IMenuTexts>('menuTexts', undefined);

export interface ITitleVariable {
	readonly name: string;
	readonly contextKey: string;
}

export interface ITitleProperties {
	isPure?: boolean;
	isAdmin?: boolean;
	prefix?: string;
}

export interface ITitlebarPart extends IDisposable {

	/**
	 * An event when the menubar visibility changes.
	 */
	readonly onMenubarVisibilityChange: Event<boolean>;

	/**
	 * Update some environmental title properties.
	 */
	updateProperties(properties: ITitleProperties): void;

	/**
	 * Adds variables to be supported in the window title.
	 */
	registerVariables(variables: ITitleVariable[]): void;
}

export class BrowserTitleService extends MultiWindowParts<BrowserTitlebarPart> implements ITitleService {

	declare _serviceBrand: undefined;

	readonly mainPart = this._register(this.createMainTitlebarPart());

	constructor(
		@IInstantiationService protected readonly instantiationService: IInstantiationService,
		@IStorageService storageService: IStorageService,
		@IThemeService themeService: IThemeService
	) {
		super('workbench.titleService', themeService, storageService);

		this._register(this.registerPart(this.mainPart));

		this.registerActions();
		this.registerAPICommands();
	}

	protected createMainTitlebarPart(): BrowserTitlebarPart {
		return this.instantiationService.createInstance(MainBrowserTitlebarPart);
	}

	private registerActions(): void {

		// Focus action
		const that = this;
		this._register(registerAction2(class FocusTitleBar extends Action2 {

			constructor() {
				super({
					id: `workbench.action.focusTitleBar`,
					title: localize2('focusTitleBar', 'Focus Title Bar'),
					category: Categories.View,
					f1: true,
					precondition: TitleBarVisibleContext
				});
			}

			run(): void {
				that.getPartByDocument(getActiveDocument())?.focus();
			}
		}));
	}

	private registerAPICommands(): void {
		this._register(CommandsRegistry.registerCommand({
			id: 'registerWindowTitleVariable',
			handler: (accessor: ServicesAccessor, name: string, contextKey: string) => {
				this.registerVariables([{ name, contextKey }]);
			},
			metadata: {
				description: 'Registers a new title variable',
				args: [
					{ name: 'name', schema: { type: 'string' }, description: 'The name of the variable to register' },
					{ name: 'contextKey', schema: { type: 'string' }, description: 'The context key to use for the value of the variable' }
				]
			}
		}));
	}

	//#region Auxiliary Titlebar Parts

	createAuxiliaryTitlebarPart(container: HTMLElement, editorGroupsContainer: IEditorGroupsContainer): IAuxiliaryTitlebarPart {
		const titlebarPartContainer = document.createElement('div');
		titlebarPartContainer.classList.add('part', 'titlebar');
		titlebarPartContainer.setAttribute('role', 'none');
		titlebarPartContainer.style.position = 'relative';
		container.insertBefore(titlebarPartContainer, container.firstChild); // ensure we are first element

		const disposables = new DisposableStore();

		const titlebarPart = this.doCreateAuxiliaryTitlebarPart(titlebarPartContainer, editorGroupsContainer);
		disposables.add(this.registerPart(titlebarPart));

		disposables.add(Event.runAndSubscribe(titlebarPart.onDidChange, () => titlebarPartContainer.style.height = `${titlebarPart.height}px`));
		titlebarPart.create(titlebarPartContainer);

		if (this.properties) {
			titlebarPart.updateProperties(this.properties);
		}

		if (this.variables.size) {
			titlebarPart.registerVariables(Array.from(this.variables.values()));
		}

		Event.once(titlebarPart.onWillDispose)(() => disposables.dispose());

		return titlebarPart;
	}

	protected doCreateAuxiliaryTitlebarPart(container: HTMLElement, editorGroupsContainer: IEditorGroupsContainer): BrowserTitlebarPart & IAuxiliaryTitlebarPart {
		return this.instantiationService.createInstance(AuxiliaryBrowserTitlebarPart, container, editorGroupsContainer, this.mainPart);
	}

	//#endregion


	//#region Service Implementation

	readonly onMenubarVisibilityChange = this.mainPart.onMenubarVisibilityChange;

	private properties: ITitleProperties | undefined = undefined;

	updateProperties(properties: ITitleProperties): void {
		this.properties = properties;

		for (const part of this.parts) {
			part.updateProperties(properties);
		}
	}

	private readonly variables = new Map<string, ITitleVariable>();

	registerVariables(variables: ITitleVariable[]): void {
		const newVariables: ITitleVariable[] = [];

		for (const variable of variables) {
			if (!this.variables.has(variable.name)) {
				this.variables.set(variable.name, variable);
				newVariables.push(variable);
			}
		}

		for (const part of this.parts) {
			part.registerVariables(newVariables);
		}
	}

	//#endregion
}

export class BrowserTitlebarPart extends Part implements ITitlebarPart {

	constructor(
		@IContextKeyService protected readonly contextKeyService: IContextKeyService
	) {
		super();
	}

	protected async getL10nTexts(): Promise<IMenuTexts> {
		const menuTexts = this.contextKeyService.getContextKeyValue<IMenuTexts>(MenuTextsContextKey.key);
		if (!menuTexts) {
			throw new Error('Menu texts not available from extension');
		}
		return menuTexts;
	}

	//#region IView

	readonly minimumWidth: number = 0;
	readonly maximumWidth: number = Number.POSITIVE_INFINITY;

	get minimumHeight(): number {
		const wcoEnabled = isWeb && isWCOEnabled();
		let value = this.isCommandCenterVisible || wcoEnabled ? DEFAULT_CUSTOM_TITLEBAR_HEIGHT : 30;
		if (wcoEnabled) {
			value = Math.max(value, getWCOTitlebarAreaRect(getWindow(this.element))?.height ?? 0);
		}

		return value / (this.preventZoom ? getZoomFactor(getWindow(this.element)) : 1);
	}

	get maximumHeight(): number { return this.minimumHeight; }

	//#endregion

	private createUserProfile(container: HTMLElement): void {
		const profileContainer = append(container, $('.user-profile'));

		const updateProfile = async () => {
			const isLoggedIn = await this.loginService.isLoggedIn();
			if (isLoggedIn) {
				// User is logged in
				profileContainer.innerHTML = `
					<span class="codicon codicon-person"></span>
				`;
				profileContainer.classList.add('logged-in');
				// 从扩展获取本地化文本
				const texts = await this.getL10nTexts();
				profileContainer.title = texts.settings;
			} else {
				// User is not logged in
				const texts = await this.getL10nTexts();
				profileContainer.innerHTML = `
					<span class="login-button">${texts.login}</span>
				`;
				profileContainer.classList.remove('logged-in');
				profileContainer.title = texts.login;
			}
		};

		// Initial update
		updateProfile();

		// Listen for login status changes
		this._register(this.loginService.onDidChangeLoginStatus(() => updateProfile()));

		// Add click handler
		this._register(addDisposableListener(profileContainer, 'click', async () => {
			const isLoggedIn = await this.loginService.isLoggedIn();
			if (isLoggedIn) {
				this.commandService.executeCommand('workbench.action.openSettings');
			} else {
				this.loginService.login();
			}
		}));
	}

	//#region Events

	private _onMenubarVisibilityChange = this._register(new Emitter<boolean>());
	readonly onMenubarVisibilityChange = this._onMenubarVisibilityChange.event;

	private readonly _onWillDispose = this._register(new Emitter<void>());
	readonly onWillDispose = this._onWillDispose.event;

	//#endregion

	protected rootContainer!: HTMLElement;
	protected dragRegion: HTMLElement | undefined;
	private title!: HTMLElement;

	private leftContent!: HTMLElement;
	private centerContent!: HTMLElement;
	private rightContent!: HTMLElement;

	protected customMenubar: CustomMenubarControl | undefined;
	protected appIcon: HTMLElement | undefined;
	private appIconBadge: HTMLElement | undefined;
	protected menubar?: HTMLElement;
	private lastLayoutDimensions: Dimension | undefined;

	private actionToolBar!: WorkbenchToolBar;
	private readonly actionToolBarDisposable = this._register(new DisposableStore());
	private readonly editorActionsChangeDisposable = this._register(new DisposableStore());
	private actionToolBarElement!: HTMLElement;

	private globalToolbarMenu = this._register(this.menuService.createMenu(MenuId.TitleBar, this.contextKeyService));
	private hasGlobalToolbarEntries = false;
	private layoutToolbarMenu: IMenu | undefined;

	private readonly globalToolbarMenuDisposables = this._register(new DisposableStore());
	private readonly editorToolbarMenuDisposables = this._register(new DisposableStore());
	private readonly layoutToolbarMenuDisposables = this._register(new DisposableStore());
	private readonly activityToolbarDisposables = this._register(new DisposableStore());

	private readonly hoverDelegate: IHoverDelegate;

	private readonly titleDisposables = this._register(new DisposableStore());
	private titleBarStyle = getTitleBarStyle(this.configurationService);

	private isInactive: boolean = false;
	private readonly isAuxiliary: boolean;

	private readonly windowTitle: WindowTitle;

	private readonly editorService: IEditorService;
	private readonly editorGroupsContainer: IEditorGroupsContainer;

	private updateService: IUpdateService;
	private userDataSyncAccountService: IUserDataSyncAccountService;

	// 获取本地化文本
	private async getL10nTexts(): Promise<IMenuTexts> {
		const menuTexts = this.contextKeyService.getContextKeyValue<IMenuTexts>(MenuTextsContextKey.key);
		if (!menuTexts) {
			throw new Error('Menu texts not available from extension');
		}
		return menuTexts;
	}

	constructor(
		id: string,
		targetWindow: CodeWindow,
		editorGroupsContainer: IEditorGroupsContainer | 'main',
		@IContextMenuService private readonly contextMenuService: IContextMenuService,
		@IConfigurationService protected readonly configurationService: IConfigurationService,
		@IBrowserWorkbenchEnvironmentService protected readonly environmentService: IBrowserWorkbenchEnvironmentService,
		@IInstantiationService protected readonly instantiationService: IInstantiationService,
		@IThemeService themeService: IThemeService,
		@IStorageService private readonly storageService: IStorageService,
		@IWorkbenchLayoutService layoutService: IWorkbenchLayoutService,
		@IContextKeyService private readonly contextKeyService: IContextKeyService,
		@IHostService private readonly hostService: IHostService,
		@IEditorGroupsService private readonly editorGroupService: IEditorGroupsService,
		@IEditorService editorService: IEditorService,
		@IMenuService private readonly menuService: IMenuService,
		@IKeybindingService private readonly keybindingService: IKeybindingService,
		@IUserDataSyncAccountService userDataSyncAccountService: IUserDataSyncAccountService,
		@ILoginService private readonly loginService: ILoginService,
		@ICommandService private readonly commandService: ICommandService,
		@IUpdateService updateService: IUpdateService
	) {
		super(id, { hasTitle: false }, themeService, storageService, layoutService);
		this.userDataSyncAccountService = userDataSyncAccountService;
		this.updateService = updateService;

		this.isAuxiliary = editorGroupsContainer !== 'main';
		this.editorService = editorService.createScoped(editorGroupsContainer, this._store);
		this.editorGroupsContainer = editorGroupsContainer === 'main' ? editorGroupService.mainPart : editorGroupsContainer;

		this.windowTitle = this._register(instantiationService.createInstance(WindowTitle, targetWindow, editorGroupsContainer));

		this.hoverDelegate = this._register(createInstantHoverDelegate());

		this.registerListeners(getWindowId(targetWindow));

		this.updateService = this.instantiationService.invokeFunction(accessor => accessor.get(IUpdateService));
	}

	private registerListeners(targetWindowId: number): void {
		this._register(this.hostService.onDidChangeFocus(focused => focused ? this.onFocus() : this.onBlur()));
		this._register(this.hostService.onDidChangeActiveWindow(windowId => windowId === targetWindowId ? this.onFocus() : this.onBlur()));
		this._register(this.configurationService.onDidChangeConfiguration(e => this.onConfigurationChanged(e)));
		this._register(this.editorGroupService.onDidChangeEditorPartOptions(e => this.onEditorPartConfigurationChange(e)));
	}

	private onBlur(): void {
		this.isInactive = true;

		this.updateStyles();
	}

	private onFocus(): void {
		this.isInactive = false;

		this.updateStyles();
	}

	private onEditorPartConfigurationChange({ oldPartOptions, newPartOptions }: IEditorPartOptionsChangeEvent): void {
		if (
			oldPartOptions.editorActionsLocation !== newPartOptions.editorActionsLocation ||
			oldPartOptions.showTabs !== newPartOptions.showTabs
		) {
			if (hasCustomTitlebar(this.configurationService, this.titleBarStyle) && this.actionToolBar) {
				this.createActionToolBar();
				this.createActionToolBarMenus({ editorActions: true });
				this._onDidChange.fire(undefined);
			}
		}
	}

	protected onConfigurationChanged(event: IConfigurationChangeEvent): void {
		// Custom menu bar (disabled if auxiliary)
		if (!this.isAuxiliary && !hasNativeTitlebar(this.configurationService, this.titleBarStyle) && (!isMacintosh || isWeb)) {
			if (event.affectsConfiguration('window.menuBarVisibility')) {
				if (this.currentMenubarVisibility === 'compact') {
					this.uninstallMenubar();
				} else {
					this.installMenubar();
				}
			}
		}

		// Actions
		if (hasCustomTitlebar(this.configurationService, this.titleBarStyle) && this.actionToolBar) {
			const affectsLayoutControl = event.affectsConfiguration(LayoutSettings.LAYOUT_ACTIONS);
			const affectsActivityControl = event.affectsConfiguration(LayoutSettings.ACTIVITY_BAR_LOCATION);

			if (affectsLayoutControl || affectsActivityControl) {
				this.createActionToolBarMenus({ layoutActions: affectsLayoutControl, activityActions: affectsActivityControl });

				this._onDidChange.fire(undefined);
			}
		}

		// Command Center
		if (event.affectsConfiguration(LayoutSettings.COMMAND_CENTER)) {
			this.createTitle();

			this._onDidChange.fire(undefined);
		}
	}

	protected installMenubar(): void {
		if (this.menubar) {
			return; // If the menubar is already installed, skip
		}

		this.customMenubar = this._register(this.instantiationService.createInstance(CustomMenubarControl));

		this.menubar = append(this.leftContent, $('div.menubar'));
		this.menubar.setAttribute('role', 'menubar');

		this._register(this.customMenubar.onVisibilityChange(e => this.onMenubarVisibilityChanged(e)));

		this.customMenubar.create(this.menubar);
	}

	private uninstallMenubar(): void {
		this.customMenubar?.dispose();
		this.customMenubar = undefined;

		this.menubar?.remove();
		this.menubar = undefined;

		this.onMenubarVisibilityChanged(false);
	}

	protected onMenubarVisibilityChanged(visible: boolean): void {
		if (isWeb || isWindows || isLinux) {
			if (this.lastLayoutDimensions) {
				this.layout(this.lastLayoutDimensions.width, this.lastLayoutDimensions.height);
			}

			this._onMenubarVisibilityChange.fire(visible);
		}
	}

	updateProperties(properties: ITitleProperties): void {
		this.windowTitle.updateProperties(properties);
	}

	registerVariables(variables: ITitleVariable[]): void {
		this.windowTitle.registerVariables(variables);
	}

	private async configureDisplayLanguage(languageId: string): Promise<void> {
		await this.instantiationService.invokeFunction(async accessor => {
			const localeService = accessor.get(ILocaleService);
			try {
				await localeService.setLocale({
					id: languageId,
					label: languageId === 'zh-cn' ? '中文(简体)' : 'English',
					galleryExtension: undefined,
					extensionId: undefined
				});
			} catch (error) {
				const notificationService = accessor.get(INotificationService);
				const texts = await this.getL10nTexts();
				notificationService.error(texts.error?.replace('{0}', error.message) || error.message);
			}
		});
	}

	protected override createContentArea(parent: HTMLElement): HTMLElement {
		try {
			this.element = parent;
			this.rootContainer = append(parent, $('.titlebar-container'));

			this.leftContent = append(this.rootContainer, $('.titlebar-left'));
			this.centerContent = append(this.rootContainer, $('.titlebar-center'));
			this.rightContent = append(this.rootContainer, $('.titlebar-right'));

			// App Icon (Windows, Linux)
			if ((isWindows || isLinux) && !hasNativeTitlebar(this.configurationService, this.titleBarStyle)) {
				this.appIcon = prepend(this.leftContent, $('a.window-appicon'));
			}

			// Draggable region that we can manipulate for #52522
			this.dragRegion = prepend(this.rootContainer, $('div.titlebar-drag-region'));

			// Menubar: install a custom menu bar depending on configuration
			if (
				!this.isAuxiliary &&
				!hasNativeTitlebar(this.configurationService, this.titleBarStyle) &&
				(!isMacintosh || isWeb) &&
				this.currentMenubarVisibility !== 'compact'
			) {
				this.installMenubar();
			}

			// Title
			this.title = append(this.centerContent, $('div.window-title'));
			this.createTitle();

			// // User Profile
			// const userProfileContainer = append(this.rightContent, $('.user-profile-container'));
			// this.createUserProfile(userProfileContainer);

			// Create Toolbar Actions
			if (hasCustomTitlebar(this.configurationService, this.titleBarStyle)) {
				this.actionToolBarElement = append(this.rightContent, $('div.action-toolbar-container'));
				this.createActionToolBar();
				this.createActionToolBarMenus();
			}

			// Log successful creation
			console.log('Titlebar content area created successfully');
		} catch (error) {
			console.error('Error creating titlebar content area:', error);
		}

		// 创建"重启更新"按钮
		const customUpdateBtn = document.createElement('div');
		customUpdateBtn.className = 'joycoder-update-btn';
		const icon = document.createElement('span');
		icon.className = 'icon';
		const circle = document.createElement('span');
		circle.className = 'circle done';
		const arrowDown = document.createElement('span');
		arrowDown.className = 'arrow-down';
		icon.appendChild(circle);
		icon.appendChild(arrowDown);
		const text = document.createElement('span');
		text.className = 'text';
		text.textContent = '重启更新';
		customUpdateBtn.appendChild(icon);
		customUpdateBtn.appendChild(text);
		customUpdateBtn.style.display = 'none';
		customUpdateBtn.onclick = () => {
			this.updateService.quitAndInstall();
		};
		// 插入到按钮组(actionToolBarElement)的最前面
		if (this.actionToolBarElement) {
			this.actionToolBarElement.insertBefore(customUpdateBtn, this.actionToolBarElement.firstChild);
		}

		// 创建"最新版本下载中…"按钮
		const downloadingBtn = document.createElement('div');
		downloadingBtn.className = 'joycoder-update-btn';
		const icon2 = document.createElement('span');
		icon2.className = 'icon';

		// SVG 环形进度条
		const progressSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
		progressSvg.setAttribute('width', '16');
		progressSvg.setAttribute('height', '16');
		progressSvg.setAttribute('viewBox', '0 0 16 16');
		progressSvg.classList.add('progress-ring');

		const bgCircle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
		bgCircle.setAttribute('cx', '8');
		bgCircle.setAttribute('cy', '8');
		bgCircle.setAttribute('r', '7');
		bgCircle.setAttribute('stroke', '#3C3C3C');
		bgCircle.setAttribute('stroke-width', '2');
		bgCircle.setAttribute('fill', 'none');

		const fgCircle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
		fgCircle.setAttribute('cx', '8');
		fgCircle.setAttribute('cy', '8');
		fgCircle.setAttribute('r', '7');
		fgCircle.setAttribute('stroke', '#F8F8F8');
		fgCircle.setAttribute('stroke-width', '2');
		fgCircle.setAttribute('fill', 'none');
		fgCircle.setAttribute('stroke-linecap', 'round');
		fgCircle.setAttribute('class', 'progress-ring__progress');

		progressSvg.appendChild(bgCircle);
		progressSvg.appendChild(fgCircle);

		icon2.appendChild(progressSvg);

		const arrowDown2 = document.createElement('span');
		arrowDown2.className = 'arrow-down';
		icon2.appendChild(arrowDown2);
		const text2 = document.createElement('span');
		text2.className = 'text';
		text2.textContent = '最新版本下载中…';
		// const arrow2 = document.createElement('span');
		// arrow2.className = 'arrow';
		// arrow2.textContent = '>';
		downloadingBtn.appendChild(icon2);
		downloadingBtn.appendChild(text2);
		// downloadingBtn.appendChild(arrow2);
		downloadingBtn.style.display = 'none';
		if (this.actionToolBarElement) {
			this.actionToolBarElement.insertBefore(downloadingBtn, this.actionToolBarElement.firstChild);
		}

		// 提供 setProgress 方法
		const setProgress = (percent: number) => {
			const radius = 7;
			const circumference = 2 * Math.PI * radius;
			fgCircle.setAttribute('stroke-dasharray', `${circumference}`);
			fgCircle.setAttribute('stroke-dashoffset', `${circumference - percent / 100 * circumference}`);
		};
		// 默认 0%
		setProgress(0);

		// 监听更新状态，动态显示按钮
		this.updateService.onStateChange(state => {
			if (state.type === StateType.Downloading && !state?.isAutoCheck) {
				downloadingBtn.style.display = '';
				customUpdateBtn.style.display = 'none';
				// @ts-ignore
				setProgress(state?.percent || 0);
			} else if (state.type === StateType.Ready) {
				downloadingBtn.style.display = 'none';
				customUpdateBtn.style.display = '';
			} else {
				downloadingBtn.style.display = 'none';
				customUpdateBtn.style.display = 'none';
			}
		});
		// 初始化时根据当前状态显示
		const state = this.updateService.state;
		if (state.type === StateType.Downloading && !state?.isAutoCheck) {
			downloadingBtn.style.display = '';
			customUpdateBtn.style.display = 'none';
		} else if (state.type === StateType.Ready) {
			downloadingBtn.style.display = 'none';
			customUpdateBtn.style.display = '';
		} else {
			downloadingBtn.style.display = 'none';
			customUpdateBtn.style.display = 'none';
		}

		// 创建齿轮设置按钮
		const settingsBtn = document.createElement('div');
		settingsBtn.className = 'joycoder-settings-btn';
		settingsBtn.style.display = 'none'; // 默认隐藏
		settingsBtn.style.alignItems = 'center';
		settingsBtn.style.justifyContent = 'center';
		settingsBtn.style.width = '16px';
		settingsBtn.style.cursor = 'pointer';
		settingsBtn.style.marginLeft = '8px';
		// 使用人形图标
		const personIcon = document.createElement('span');
		personIcon.className = 'codicon codicon-account';
		personIcon.style.fontSize = '16px';
		personIcon.style.color = 'var(--vscode-icon-foreground)';
		settingsBtn.appendChild(personIcon);
		settingsBtn.title = '设置';

		// 根据登录状态控制显示
		const updateSettingsButton = async () => {
			const isLoggedIn = await this.loginService.isLoggedIn();
			settingsBtn.style.display = isLoggedIn ? 'flex' : 'none';
		};

		// 初始状态
		updateSettingsButton();

		// 监听登录状态变化
		this._register(this.loginService.onDidChangeLoginStatus(() => {
			updateSettingsButton();
		}));
		settingsBtn.onclick = async (e) => {
			let hideTimeout: number | undefined;

			// 判断菜单是否已存在
			const oldMenu = document.getElementById('joycoder-settings-menu');
			if (oldMenu) {
				oldMenu.remove(); // 已展开则收起
				personIcon.classList.remove('active');
				return;
			}

			// 获取用户信息和当前语言
			const isLoggedIn = await this.loginService.isLoggedIn();
			const userInfo = await this.loginService.getLoginInfo();
			const currentLanguage = getNLSLanguage() || language;
			console.log(currentLanguage, 88);



			const menuContent = `
						<div>
							<div class="joycoder-menu-item user-profile" data-action="profile">
								<div class="user-info">
									<span class="codicon codicon-account"></span>
									<span class="user-name">${userInfo?.userName}</span>
									<span class="codicon codicon-chevron-right"></span>
								</div>
								<!--
								<div class="user-details">
									<span class="company-name">大米科技有限公司</span>
								</div>
								-->
								<div class="submenu user-options">
									<div class="joycoder-menu-item" data-action="logout">
										<span class="codicon codicon-sign-out"></span>
										<span>${(await this.getL10nTexts()).logout}</span>
									</div>
								</div>
							</div>
							<div class="joycoder-menu-divider"></div>
							<div class="joycoder-menu-item" data-action="theme">
								<span class="codicon codicon-jersey"></span>
								<span>${(await this.getL10nTexts()).theme}</span>
							</div>
							<div class="joycoder-menu-item language-menu" data-action="language">
								<span class="codicon codicon-globe"></span>
								<span>${(await this.getL10nTexts()).language}</span>
								<span class="selected-language">${currentLanguage === 'zh-cn' ? '中文(简体)' : 'English'}</span>
								<span class="codicon codicon-chevron-right"></span>
								<div class="submenu language-options">
									<div class="joycoder-menu-item${currentLanguage === 'zh-cn' ? ' active' : ''}" data-action="language-zh" data-lang="中文">中文(简体)</div>
									<div class="joycoder-menu-item${currentLanguage === 'en' ? ' active' : ''}" data-action="language-en" data-lang="English">English</div>
								</div>
							</div>
							<div class="joycoder-menu-item" data-action="keybindings">
								<span class="codicon codicon-keyboard"></span>
								<span>${(await this.getL10nTexts()).keybindings}</span>
							</div>
							<div class="joycoder-menu-divider"></div>
							<div class="joycoder-menu-item" data-action="settings">
								<span class="codicon codicon-settings-gear"></span>
								<span>${(await this.getL10nTexts()).settings}</span>
							</div>
							<div class="joycoder-menu-item" data-action="joycoder">
								<span class="codicon codicon-tools"></span>
								<span>${(await this.getL10nTexts()).joycodeSettings}</span>
							</div>
							<div class="joycoder-menu-item" data-action="extensions">
								<span class="codicon codicon-extensions"></span>
								<span>${(await this.getL10nTexts()).extensions}</span>
							</div>
							<div class="joycoder-menu-divider"></div>
							<div class="joycoder-menu-item" data-action="releaseNote">
								<span class="codicon codicon-list-unordered"></span>
								<span>${(await this.getL10nTexts()).releaseNote}</span>
							</div>
							<div class="joycoder-menu-item" data-action="about">
								<span class="codicon codicon-info"></span>
								<span>${(await this.getL10nTexts()).about}</span>
							</div>
							<div class="joycoder-menu-item" data-action="update">
								<span class="codicon codicon-arrow-circle-up"></span>
								<span>${(await this.getL10nTexts()).checkUpdate}</span>
							</div>
						</div>
					`;

			// 创建并设置菜单
			const menu = document.createElement('div');
			menu.id = 'joycoder-settings-menu';
			menu.className = 'joycoder-settings-menu';

			// 设置菜单内容并添加到文档
			DOM.safeInnerHtml(menu, menuContent);
			document.body.appendChild(menu);
			menu.style.top = `${settingsBtn.offsetTop + settingsBtn.offsetHeight + 4}px`;

			// 使用事件代理处理submenu的显示和隐藏
			menu.addEventListener('mouseover', (e) => {
				const menuItem = (e.target as HTMLElement).closest('.joycoder-menu-item') as HTMLElement;
				if (!menuItem?.querySelector('.submenu')) return;

				// 先隐藏所有其他submenu
				const allSubmenus = menu.querySelectorAll('.submenu') as NodeListOf<HTMLElement>;
				allSubmenus.forEach(sub => {
					if (sub.parentElement !== menuItem) {
						sub.style.display = 'none';
					}
				});

				if (hideTimeout) {
					clearTimeout(hideTimeout);
					hideTimeout = undefined;
				}
				const submenu = menuItem.querySelector('.submenu') as HTMLElement;
				submenu.style.display = 'block';
			});

			menu.addEventListener('mouseout', (e) => {
				const menuItem = (e.target as HTMLElement).closest('.joycoder-menu-item') as HTMLElement;
				if (!menuItem?.querySelector('.submenu')) return;

				const submenu = menuItem.querySelector('.submenu') as HTMLElement;
				const relatedTarget = e.relatedTarget as HTMLElement;

				// 只有当鼠标不是移动到submenu或其他菜单项时才启动隐藏计时器
				if (!menuItem.contains(relatedTarget)) {
					hideTimeout = window.setTimeout(() => {
						if (!menuItem.matches(':hover')) {
							submenu.style.display = 'none';
						}
					}, 300);
				}
			});

			// 使用事件代理处理菜单点击
			menu.addEventListener('click', (ev) => {
				const target = ev.target as HTMLElement;
				const menuItem = target.closest('.joycoder-menu-item') as HTMLElement;
				if (!menuItem) {
					return;
				}
				const action = menuItem.dataset.action;
				// 这里根据 action 执行不同操作
				if (action === 'login') {
					// 登录账号
					this.instantiationService.invokeFunction(accessor => {
						const loginService = accessor.get(ILoginService) as ILoginService;
						if (loginService && typeof loginService.login === 'function') {
							loginService.login();
						}
					});
				} else if (action === 'logout') {
					// 退出登录
					this.instantiationService.invokeFunction(accessor => {
						const loginService = accessor.get(ILoginService) as ILoginService;
						if (loginService && typeof loginService.logout === 'function') {
							loginService.logout();
						}
					});
				} else if (action === 'language-zh' || action === 'language-en') {
					// 切换语言
					this.configureDisplayLanguage(action === 'language-zh' ? 'zh-cn' : 'en');
				} else if (action === 'settings') {
					this.instantiationService.invokeFunction(accessor => {
						const commandService = accessor.get(ICommandService) as ICommandService;
						commandService.executeCommand('workbench.action.openSettings');
					});
				} else if (action === 'theme') {
					this.instantiationService.invokeFunction(accessor => {
						const commandService = accessor.get(ICommandService) as ICommandService;
						commandService.executeCommand('workbench.action.selectTheme');
					});
				} else if (action === 'update') {
					this.instantiationService.invokeFunction(accessor => {
						const commandService = accessor.get(ICommandService) as ICommandService;
						commandService.executeCommand('update.check');
					});
				} else if (action === 'about') {
					this.instantiationService.invokeFunction(accessor => {
						const commandService = accessor.get(ICommandService) as ICommandService;
						commandService.executeCommand('workbench.action.showAboutDialog');
					});
				} else if (action === 'keybindings') {
					this.instantiationService.invokeFunction(accessor => {
						const commandService = accessor.get(ICommandService) as ICommandService;
						commandService.executeCommand('workbench.action.openGlobalKeybindings');
					});
				} else if (action === 'extensions') {
					this.instantiationService.invokeFunction(accessor => {
						const commandService = accessor.get(ICommandService) as ICommandService;
						commandService.executeCommand('workbench.view.extensions');
					});
				} else if (action === 'project') {
					// 项目设置，暂未实现
				} else if (action === 'joycoder') {
					this.instantiationService.invokeFunction(accessor => {
						const commandService = accessor.get(ICommandService) as ICommandService;
						commandService.executeCommand('workbench.action.openJoyCoderSettings');
					});
				}
				menu.remove();
			});

			// 点击外部关闭
			setTimeout(() => {
				const closeMenu = (ev: MouseEvent) => {
					if (!menu.contains(ev.target as Node) && !settingsBtn.contains(ev.target as Node)) {
						menu.remove();
						personIcon.classList.remove('active');
						document.removeEventListener('mousedown', closeMenu);
					}
				};
				document.addEventListener('mousedown', closeMenu);
			}, 10);
		};
		// 插入到 actionToolBarElement（右上角按钮组）
		if (this.actionToolBarElement) {
			this.actionToolBarElement.appendChild(settingsBtn);
		} else {
			this.rightContent.appendChild(settingsBtn);
		}

		// Window Controls Container
		if (!hasNativeTitlebar(this.configurationService, this.titleBarStyle)) {
			let primaryWindowControlsLocation = isMacintosh ? 'left' : 'right';
			if (isMacintosh && isNative) {

				// Check if the locale is RTL, macOS will move traffic lights in RTL locales
				// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/textInfo

				const localeInfo = safeIntl.Locale(platformLocale) as any;
				if (localeInfo?.textInfo?.direction === 'rtl') {
					primaryWindowControlsLocation = 'right';
				}
			}

			if (isMacintosh && isNative && primaryWindowControlsLocation === 'left') {
				// macOS native: controls are on the left and the container is not needed to make room
				// for something, except for web where a custom menu being supported). not putting the
				// container helps with allowing to move the window when clicking very close to the
				// window control buttons.
			} else {
				const windowControlsContainer = append(primaryWindowControlsLocation === 'left' ? this.leftContent : this.rightContent, $('div.window-controls-container'));
				if (isWeb) {
					// Web: its possible to have control overlays on both sides, for example on macOS
					// with window controls on the left and PWA controls on the right.
					append(primaryWindowControlsLocation === 'left' ? this.rightContent : this.leftContent, $('div.window-controls-container'));
				}

				if (isWCOEnabled()) {
					windowControlsContainer.classList.add('wco-enabled');
				}
			}
		}

		// Context menu over title bar: depending on the OS and the location of the click this will either be
		// the overall context menu for the entire title bar or a specific title context menu.
		// Windows / Linux: we only support the overall context menu on the title bar
		// macOS: we support both the overall context menu and the title context menu.
		//        in addition, we allow Cmd+click to bring up the title context menu.
		{
			this._register(addDisposableListener(this.rootContainer, EventType.CONTEXT_MENU, e => {
				EventHelper.stop(e);

				let targetMenu: MenuId;
				if (isMacintosh && isHTMLElement(e.target) && isAncestor(e.target, this.title)) {
					targetMenu = MenuId.TitleBarTitleContext;
				} else {
					targetMenu = MenuId.TitleBarContext;
				}

				this.onContextMenu(e, targetMenu);
			}));

			if (isMacintosh) {
				this._register(addDisposableListener(this.title, EventType.MOUSE_DOWN, e => {
					if (e.metaKey) {
						EventHelper.stop(e, true /* stop bubbling to prevent command center from opening */);

						this.onContextMenu(e, MenuId.TitleBarTitleContext);
					}
				}, true /* capture phase to prevent command center from opening */));
			}
		}

		this.updateStyles();

		return this.element;
	}

	private createTitle(): void {
		this.titleDisposables.clear();

		// Text Title
		if (!this.isCommandCenterVisible) {
			this.title.innerText = this.windowTitle.value;
			this.titleDisposables.add(this.windowTitle.onDidChange(() => {
				this.title.innerText = this.windowTitle.value;
				// layout menubar and other renderings in the titlebar
				if (this.lastLayoutDimensions) {
					this.updateLayout(this.lastLayoutDimensions);
				}
			}));
		}

		// Menu Title
		else {
			const commandCenter = this.instantiationService.createInstance(CommandCenterControl, this.windowTitle, this.hoverDelegate);
			reset(this.title, commandCenter.element);
			this.titleDisposables.add(commandCenter);
		}
	}

	private actionViewItemProvider(action: IAction, options: IBaseActionViewItemOptions): IActionViewItem | undefined {

		// --- Activity Actions
		if (!this.isAuxiliary) {
			if (action.id === GLOBAL_ACTIVITY_ID) {
				return this.instantiationService.createInstance(SimpleGlobalActivityActionViewItem, { position: () => HoverPosition.BELOW }, options);
			}
			if (action.id === ACCOUNTS_ACTIVITY_ID) {
				return this.instantiationService.createInstance(SimpleAccountActivityActionViewItem, { position: () => HoverPosition.BELOW }, options);
			}
		}

		// --- Editor Actions
		const activeEditorPane = this.editorGroupsContainer.activeGroup?.activeEditorPane;
		if (activeEditorPane && activeEditorPane instanceof EditorPane) {
			const result = activeEditorPane.getActionViewItem(action, options);

			if (result) {
				return result;
			}
		}

		// Check extensions
		return createActionViewItem(this.instantiationService, action, { ...options, menuAsChild: false });
	}

	private getKeybinding(action: IAction): ResolvedKeybinding | undefined {
		const editorPaneAwareContextKeyService = this.editorGroupsContainer.activeGroup?.activeEditorPane?.scopedContextKeyService ?? this.contextKeyService;

		return this.keybindingService.lookupKeybinding(action.id, editorPaneAwareContextKeyService);
	}

	private createActionToolBar() {

		// Creates the action tool bar. Depends on the configuration of the title bar menus
		// Requires to be recreated whenever editor actions enablement changes

		this.actionToolBarDisposable.clear();

		this.actionToolBar = this.actionToolBarDisposable.add(this.instantiationService.createInstance(WorkbenchToolBar, this.actionToolBarElement, {
			contextMenu: MenuId.TitleBarContext,
			orientation: ActionsOrientation.HORIZONTAL,
			ariaLabel: localize('ariaLabelTitleActions', "Title actions"),
			getKeyBinding: action => this.getKeybinding(action),
			overflowBehavior: { maxItems: 9, exempted: [ACCOUNTS_ACTIVITY_ID, GLOBAL_ACTIVITY_ID, ...EDITOR_CORE_NAVIGATION_COMMANDS] },
			anchorAlignmentProvider: () => AnchorAlignment.RIGHT,
			telemetrySource: 'titlePart',
			highlightToggledItems: this.editorActionsEnabled, // Only show toggled state for editor actions (Layout actions are not shown as toggled)
			actionViewItemProvider: (action, options) => this.actionViewItemProvider(action, options),
			hoverDelegate: this.hoverDelegate
		}));

		if (this.editorActionsEnabled) {
			this.actionToolBarDisposable.add(this.editorGroupsContainer.onDidChangeActiveGroup(() => this.createActionToolBarMenus({ editorActions: true })));
		}
	}

	private createActionToolBarMenus(update: true | { editorActions?: boolean; layoutActions?: boolean; activityActions?: boolean } = true) {
		if (update === true) {
			update = { editorActions: true, layoutActions: true, activityActions: true };
		}

		const updateToolBarActions = () => {
			const actions: IToolbarActions = { primary: [], secondary: [] };

			// --- Editor Actions
			if (this.editorActionsEnabled) {
				this.editorActionsChangeDisposable.clear();

				const activeGroup = this.editorGroupsContainer.activeGroup;
				if (activeGroup) {
					const editorActions = activeGroup.createEditorActions(this.editorActionsChangeDisposable);

					actions.primary.push(...editorActions.actions.primary);
					actions.secondary.push(...editorActions.actions.secondary);

					this.editorActionsChangeDisposable.add(editorActions.onDidChange(() => updateToolBarActions()));
				}
			}

			// --- Global Actions
			const globalToolbarActions = this.globalToolbarMenu.getActions();
			this.hasGlobalToolbarEntries = globalToolbarActions.length > 0;
			fillInActionBarActions(
				globalToolbarActions,
				actions
			);

			// --- Layout Actions
			if (this.layoutToolbarMenu) {
				fillInActionBarActions(
					this.layoutToolbarMenu.getActions(),
					actions,
					() => !this.editorActionsEnabled // Layout Actions in overflow menu when editor actions enabled in title bar
				);
			}

			// --- Activity Actions (always at the end)
			if (this.activityActionsEnabled) {
				if (isAccountsActionVisible(this.storageService)) {
					actions.primary.push(ACCOUNTS_ACTIVITY_TILE_ACTION);
				}

				actions.primary.push(GLOBAL_ACTIVITY_TITLE_ACTION);
			}

			this.actionToolBar.setActions(prepareActions(actions.primary), prepareActions(actions.secondary));
		};

		// Create/Update the menus which should be in the title tool bar

		if (update.editorActions) {
			this.editorToolbarMenuDisposables.clear();

			// The editor toolbar menu is handled by the editor group so we do not need to manage it here.
			// However, depending on the active editor, we need to update the context and action runner of the toolbar menu.
			if (this.editorActionsEnabled && this.editorService.activeEditor !== undefined) {
				const context: IEditorCommandsContext = { groupId: this.editorGroupsContainer.activeGroup.id };

				this.actionToolBar.actionRunner = this.editorToolbarMenuDisposables.add(new EditorCommandsContextActionRunner(context));
				this.actionToolBar.context = context;
			} else {
				this.actionToolBar.actionRunner = this.editorToolbarMenuDisposables.add(new ActionRunner());
				this.actionToolBar.context = undefined;
			}
		}

		if (update.layoutActions) {
			this.layoutToolbarMenuDisposables.clear();

			if (this.layoutControlEnabled) {
				this.layoutToolbarMenu = this.menuService.createMenu(MenuId.LayoutControlMenu, this.contextKeyService);

				this.layoutToolbarMenuDisposables.add(this.layoutToolbarMenu);
				this.layoutToolbarMenuDisposables.add(this.layoutToolbarMenu.onDidChange(() => updateToolBarActions()));
			} else {
				this.layoutToolbarMenu = undefined;
			}
		}

		this.globalToolbarMenuDisposables.clear();
		this.globalToolbarMenuDisposables.add(this.globalToolbarMenu.onDidChange(() => updateToolBarActions()));

		if (update.activityActions) {
			this.activityToolbarDisposables.clear();
			if (this.activityActionsEnabled) {
				this.activityToolbarDisposables.add(this.storageService.onDidChangeValue(StorageScope.PROFILE, AccountsActivityActionViewItem.ACCOUNTS_VISIBILITY_PREFERENCE_KEY, this._store)(() => updateToolBarActions()));
			}
		}

		updateToolBarActions();
	}

	override updateStyles(): void {
		super.updateStyles();

		// Part container
		if (this.element) {
			if (this.isInactive) {
				this.element.classList.add('inactive');
			} else {
				this.element.classList.remove('inactive');
			}

			const titleBackground = this.getColor(this.isInactive ? TITLE_BAR_INACTIVE_BACKGROUND : TITLE_BAR_ACTIVE_BACKGROUND, (color, theme) => {
				// LCD Rendering Support: the title bar part is a defining its own GPU layer.
				// To benefit from LCD font rendering, we must ensure that we always set an
				// opaque background color. As such, we compute an opaque color given we know
				// the background color is the workbench background.
				return color.isOpaque() ? color : color.makeOpaque(WORKBENCH_BACKGROUND(theme));
			}) || '';
			this.element.style.backgroundColor = titleBackground;

			if (this.appIconBadge) {
				this.appIconBadge.style.backgroundColor = titleBackground;
			}

			if (titleBackground && Color.fromHex(titleBackground).isLighter()) {
				this.element.classList.add('light');
			} else {
				this.element.classList.remove('light');
			}

			const titleForeground = this.getColor(this.isInactive ? TITLE_BAR_INACTIVE_FOREGROUND : TITLE_BAR_ACTIVE_FOREGROUND);
			this.element.style.color = titleForeground || '';

			const titleBorder = this.getColor(TITLE_BAR_BORDER);
			this.element.style.borderBottom = titleBorder ? `1px solid ${titleBorder} ` : '';
		}
	}

	protected onContextMenu(e: MouseEvent, menuId: MenuId): void {
		const event = new StandardMouseEvent(getWindow(this.element), e);

		// Show it
		this.contextMenuService.showContextMenu({
			getAnchor: () => event,
			menuId,
			contextKeyService: this.contextKeyService,
			domForShadowRoot: isMacintosh && isNative ? event.target : undefined
		});
	}

	protected get currentMenubarVisibility(): MenuBarVisibility {
		if (this.isAuxiliary) {
			return 'hidden';
		}

		return getMenuBarVisibility(this.configurationService);
	}

	private get layoutControlEnabled(): boolean {
		return !this.isAuxiliary && this.configurationService.getValue<boolean>(LayoutSettings.LAYOUT_ACTIONS) !== false;
	}

	protected get isCommandCenterVisible() {
		return this.configurationService.getValue<boolean>(LayoutSettings.COMMAND_CENTER) !== false;
	}

	private get editorActionsEnabled(): boolean {
		return this.editorGroupService.partOptions.editorActionsLocation === EditorActionsLocation.TITLEBAR ||
			(
				this.editorGroupService.partOptions.editorActionsLocation === EditorActionsLocation.DEFAULT &&
				this.editorGroupService.partOptions.showTabs === EditorTabsMode.NONE
			);
	}

	private get activityActionsEnabled(): boolean {
		const activityBarPosition = this.configurationService.getValue<ActivityBarPosition>(LayoutSettings.ACTIVITY_BAR_LOCATION);
		return !this.isAuxiliary && (activityBarPosition === ActivityBarPosition.TOP || activityBarPosition === ActivityBarPosition.BOTTOM);
	}

	get hasZoomableElements(): boolean {
		const hasMenubar = !(this.currentMenubarVisibility === 'hidden' || this.currentMenubarVisibility === 'compact' || (!isWeb && isMacintosh));
		const hasCommandCenter = this.isCommandCenterVisible;
		const hasToolBarActions = this.hasGlobalToolbarEntries || this.layoutControlEnabled || this.editorActionsEnabled || this.activityActionsEnabled;
		return hasMenubar || hasCommandCenter || hasToolBarActions;
	}

	get preventZoom(): boolean {
		// Prevent zooming behavior if any of the following conditions are met:
		// 1. Shrinking below the window control size (zoom < 1)
		// 2. No custom items are present in the title bar

		return getZoomFactor(getWindow(this.element)) < 1 || !this.hasZoomableElements;
	}

	override layout(width: number, height: number): void {
		this.updateLayout(new Dimension(width, height));

		super.layoutContents(width, height);
	}

	private updateLayout(dimension: Dimension): void {
		this.lastLayoutDimensions = dimension;

		if (hasCustomTitlebar(this.configurationService, this.titleBarStyle)) {
			const zoomFactor = getZoomFactor(getWindow(this.element));

			this.element.style.setProperty('--zoom-factor', zoomFactor.toString());
			this.rootContainer.classList.toggle('counter-zoom', this.preventZoom);

			if (this.customMenubar) {
				const menubarDimension = new Dimension(0, dimension.height);
				this.customMenubar.layout(menubarDimension);
			}
		}
	}

	focus(): void {
		if (this.customMenubar) {
			this.customMenubar.toggleFocus();
		} else {
			(this.element.querySelector('[tabindex]:not([tabindex="-1"])') as HTMLElement | null)?.focus();
		}
	}

	toJSON(): object {
		return {
			type: Parts.TITLEBAR_PART
		};
	}

	override dispose(): void {
		this._onWillDispose.fire();

		super.dispose();
	}
}

export class MainBrowserTitlebarPart extends BrowserTitlebarPart {

	constructor(
		@IContextMenuService contextMenuService: IContextMenuService,
		@IConfigurationService configurationService: IConfigurationService,
		@IBrowserWorkbenchEnvironmentService environmentService: IBrowserWorkbenchEnvironmentService,
		@IInstantiationService instantiationService: IInstantiationService,
		@IThemeService themeService: IThemeService,
		@IStorageService storageService: IStorageService,
		@IWorkbenchLayoutService layoutService: IWorkbenchLayoutService,
		@IContextKeyService contextKeyService: IContextKeyService,
		@IHostService hostService: IHostService,
		@IEditorGroupsService editorGroupService: IEditorGroupsService,
		@IEditorService editorService: IEditorService,
		@IMenuService menuService: IMenuService,
		@IKeybindingService keybindingService: IKeybindingService,
		@IUserDataSyncAccountService userDataSyncAccountService: IUserDataSyncAccountService,
		@ILoginService loginService: ILoginService,
		@ICommandService commandService: ICommandService,
		@IUpdateService updateService: IUpdateService
	) {
		super(Parts.TITLEBAR_PART, mainWindow, 'main', contextMenuService, configurationService, environmentService, instantiationService, themeService, storageService, layoutService, contextKeyService, hostService, editorGroupService, editorService, menuService, keybindingService, userDataSyncAccountService, loginService, commandService, updateService);
	}
}

export interface IAuxiliaryTitlebarPart extends ITitlebarPart, IView {
	readonly container: HTMLElement;
	readonly height: number;
}

export class AuxiliaryBrowserTitlebarPart extends BrowserTitlebarPart implements IAuxiliaryTitlebarPart {

	private static COUNTER = 1;

	get height() { return this.minimumHeight; }

	constructor(
		readonly container: HTMLElement,
		editorGroupsContainer: IEditorGroupsContainer,
		private readonly mainTitlebar: BrowserTitlebarPart,
		@IContextMenuService contextMenuService: IContextMenuService,
		@IConfigurationService configurationService: IConfigurationService,
		@IBrowserWorkbenchEnvironmentService environmentService: IBrowserWorkbenchEnvironmentService,
		@IInstantiationService instantiationService: IInstantiationService,
		@IThemeService themeService: IThemeService,
		@IStorageService storageService: IStorageService,
		@IWorkbenchLayoutService layoutService: IWorkbenchLayoutService,
		@IContextKeyService contextKeyService: IContextKeyService,
		@IHostService hostService: IHostService,
		@IEditorGroupsService editorGroupService: IEditorGroupsService,
		@IEditorService editorService: IEditorService,
		@IMenuService menuService: IMenuService,
		@IKeybindingService keybindingService: IKeybindingService,
		@IUserDataSyncAccountService userDataSyncAccountService: IUserDataSyncAccountService,
		@ILoginService loginService: ILoginService,
		@ICommandService commandService: ICommandService,
		@IUpdateService updateService: IUpdateService
	) {
		const id = AuxiliaryBrowserTitlebarPart.COUNTER++;
		super(`workbench.parts.auxiliaryTitle.${id} `, getWindow(container), editorGroupsContainer, contextMenuService, configurationService, environmentService, instantiationService, themeService, storageService, layoutService, contextKeyService, hostService, editorGroupService, editorService, menuService, keybindingService, userDataSyncAccountService, loginService, commandService, updateService);
	}

	override get preventZoom(): boolean {

		// Prevent zooming behavior if any of the following conditions are met:
		// 1. Shrinking below the window control size (zoom < 1)
		// 2. No custom items are present in the main title bar
		// The auxiliary title bar never contains any zoomable items itself,
		// but we want to match the behavior of the main title bar.

		return getZoomFactor(getWindow(this.element)) < 1 || !this.mainTitlebar.hasZoomableElements;
	}
}
























{"joyCoderVersion": "0.6.0", "version": "1.98.2", "nameShort": "JoyCode", "nameLong": "JoyCode", "applicationName": "joycode", "dataFolderName": ".joycode-editor", "win32MutexName": "joycode", "licenseName": "MIT", "licenseUrl": "https://github.com/JoyCode/joycode/blob/master/LICENSE", "serverLicenseUrl": "", "serverGreeting": [], "serverLicense": [], "serverLicensePrompt": "", "serverApplicationName": "joycoder-server", "serverDataFolderName": ".joycoder-server", "tunnelApplicationName": "joycoder-tunnel", "win32DirName": "JoyCode", "win32NameVersion": "JoyCode", "win32RegValueName": "JoyCode", "win32x64AppId": "{{9D394D01-1728-45A7-B997-A6C82C5452C3}", "win32arm64AppId": "{{0668DD58-2BDE-4101-8CDA-40252DF8875D}", "win32x64UserAppId": "{{8BED5DC1-6C55-46E6-9FE6-18F7E6F7C7F1}", "win32arm64UserAppId": "{{F6C87466-BC82-4A8F-B0FF-18CA366BA4D8}", "win32AppUserModelId": "JoyCode.Editor", "win32ShellNameShort": "JoyCode", "win32TunnelServiceMutex": "joycoder-tunnelservice", "win32TunnelMutex": "joycoder-tunnel", "darwinBundleIdentifier": "com.joycodereditor.code", "linuxIconName": "joycoder-editor", "licenseFileName": "LICENSE.txt", "reportIssueUrl": "https://github.com/JoyCode/joycode/issues/new", "nodejsRepository": "https://nodejs.org", "urlProtocol": "joycoder", "telemetryDisabled": true, "defaultLocale": "zh-cn", "builtInExtensions": [{"name": "joycoder.joycode-l10n", "version": "0.0.1", "repo": "local", "metadata": {"id": "joycode-l10n", "publisherId": "joycoder", "publisherDisplayName": "JoyCode"}}], "webviewContentExternalBaseUrlTemplate": "https://{{uuid}}.vscode-cdn.net/insider/ef65ac1ba57f57f2a3961bfe94aa20481caca4c6/out/vs/workbench/contrib/webview/browser/pre/", "checksumFailMoreInfoUrl": "", "documentationUrl": "", "extensionsGallery": {"serviceUrl": "https://open-vsx.org/vscode/gallery", "itemUrl": "https://open-vsx.org/vscode/item", "extensionUrlTemplate": "https://open-vsx.org/vscode/gallery/{publisher}/{name}/latest"}, "introductoryVideosUrl": "", "keyboardShortcutsUrlLinux": "", "keyboardShortcutsUrlMac": "", "keyboardShortcutsUrlWin": "", "linkProtectionTrustedDomains": ["https://open-vsx.org"], "releaseNotesUrl": "", "requestFeatureUrl": "", "tipsAndTricksUrl": "", "twitterUrl": "", "updateUrl": "https://joycoder-api-inner-pr.jd.com/api/saas/ideVersion/v1/version/joycoder-ide", "joyCoderBaseUrl": "https://joycoder-api-inner-pr.jd.com", "joyCoderEnv": "pre", "joyCoderCloudBaseUrl": "http://*************", "joyCoderLoginUrl": "https://pre-joycoder.jd.com/login?ideAppName=JoyCode&fromIde=ide&redirect=0", "downloadUrl": "https://github.com/JoyCode/joycoder/releases", "quality": "stable", "win32AppId": "{{763CBF88-25C6-4B10-952F-326AE657F16B}", "win32UserAppId": "{{0FD05EB4-651E-4E78-A062-515204B47A3A}", "baseGlobalName": "JoyCode.IDE", "baseGlobalUserKey": "joyCoderUser", "joyCoderExtGlobalName": "JoyCode.joycoder-editor", "joyCoderExtGlobalUserKey": "jdhLoginInfo", "commit": "bf58862d8d60ad8747b8588ad1b8c1ae956ddad6", "serverDownloadUrlTemplate": "https://aichat.s3-ipv6.cn-north-1.jdcloud-oss.com/JoyCoderIDE/0.6.0/joycoder-reh-linux-latest.tar.gz", "extensionAllowedBadgeProviders": ["api.bintray.com", "api.travis-ci.com", "api.travis-ci.org", "app.fossa.io", "badge.buildkite.com", "badge.fury.io", "badge.waffle.io", "badgen.net", "badges.frapsoft.com", "badges.gitter.im", "badges.greenkeeper.io", "cdn.travis-ci.com", "cdn.travis-ci.org", "ci.appveyor.com", "circleci.com", "cla.opensource.microsoft.com", "codacy.com", "codeclimate.com", "codecov.io", "coveralls.io", "david-dm.org", "deepscan.io", "dev.azure.com", "docs.rs", "flat.badgen.net", "gemnasium.com", "githost.io", "gitlab.com", "godoc.org", "goreportcard.com", "img.shields.io", "isitmaintained.com", "marketplace.visualstudio.com", "nodesecurity.io", "opencollective.com", "snyk.io", "travis-ci.com", "travis-ci.org", "visualstudio.com", "vsmarketplacebadge.apphb.com", "www.bithound.io", "www.versioneye.com"], "extensionAllowedBadgeProvidersRegex": ["^https:\\/\\/github\\.com\\/[^/]+\\/[^/]+\\/(actions\\/)?workflows\\/.*badge\\.svg"], "extensionEnabledApiProposals": {"ms-vscode.vscode-selfhost-test-provider": ["testObserver", "testRelatedCode"], "VisualStudioExptTeam.vscodeintellicode-completions": ["inlineCompletionsAdditions"], "ms-vsliveshare.vsliveshare": ["contribMenuBarHome", "contribShareMenu", "contribStatusBarItems", "diffCommand", "documentFiltersExclusive", "fileSearchProvider", "findTextInFiles", "notebookCellExecutionState", "notebookLiveShare", "terminalDimensions", "terminalDataWriteEvent", "textSearchProvider"], "ms-vscode.js-debug": ["portsAttributes", "findTextInFiles", "workspaceTrust", "tunnels"], "ms-toolsai.vscode-ai-remote": ["resolvers"], "ms-python.python": ["codeActionAI", "contribEditorContentMenu", "quickPickSortByLabel", "portsAttributes", "testObserver", "quickPickItemTooltip", "terminalDataWriteEvent", "terminalExecuteCommandEvent", "notebookReplDocument", "notebookVariableProvider", "terminalShellEnv", "terminalShellType"], "ms-python.vscode-python-envs": ["terminalShellEnv", "terminalShellType"], "ms-dotnettools.dotnet-interactive-vscode": ["notebookMessaging"], "GitHub.codespaces": ["contribEditSessions", "contribMenuBarHome", "contribRemoteHelp", "contribViewsRemote", "resolvers", "tunnels", "terminalDataWriteEvent", "treeViewReveal", "notebookKernelSource"], "ms-vscode.azure-repos": ["extensionRuntime", "fileSearchProvider", "textSearchProvider"], "ms-vscode.remote-repositories": ["canonicalUri<PERSON>rovider", "contribEditSessions", "contribRemoteHelp", "contribMenuBarHome", "contribViewsRemote", "contribViewsWelcome", "contribShareMenu", "documentFiltersExclusive", "editSessionIdentityProvider", "extensionRuntime", "fileSearchProvider", "quickPickSortByLabel", "workspaceTrust", "shareProvider", "scmActionButton", "scmSelectedProvider", "scmValidation", "textSearchProvider", "timeline"], "ms-vscode-remote.remote-wsl": ["resolvers", "contribRemoteHelp", "contribViewsRemote", "telemetry"], "ms-vscode-remote.remote-ssh": ["resolvers", "tunnels", "terminalDataWriteEvent", "contribRemoteHelp", "contribViewsRemote", "telemetry"], "ms-vscode.remote-server": ["resolvers", "tunnels", "contribViewsWelcome"], "ms-vscode.remote-explorer": ["contribRemoteHelp", "contribViewsRemote", "extensionsAny"], "ms-vscode-remote.remote-containers": ["contribEditSessions", "resolvers", "portsAttributes", "tunnels", "workspaceTrust", "terminalDimensions", "contribRemoteHelp", "contribViewsRemote"], "ms-vscode.js-debug-nightly": ["portsAttributes", "findTextInFiles", "workspaceTrust", "tunnels"], "ms-vscode.lsif-browser": ["documentFiltersExclusive"], "ms-vscode.vscode-speech": ["speech"], "GitHub.vscode-pull-request-github": ["activeComment", "codiconDecoration", "codeActionRanges", "commentingRangeHint", "commentReactor", "commentReveal", "commentThreadApplicability", "contribAccessibilityHelpContent", "contribCommentEditorActionsMenu", "contribCommentPeekContext", "contribCommentThreadAdditionalMenu", "contribCommentsViewThreadMenus", "contribEditorContentMenu", "contribMultiDiffEditorMenus", "contribShareMenu", "diffCommand", "quickDiffProvider", "shareProvider", "tabInputTextMerge", "tokenInformation", "treeViewMarkdownMessage"], "GitHub.remotehub": ["contribRemoteHelp", "contribMenuBarHome", "contribViewsRemote", "contribViewsWelcome", "documentFiltersExclusive", "extensionRuntime", "fileSearchProvider", "quickPickSortByLabel", "workspaceTrust", "scmSelectedProvider", "scmValidation", "textSearchProvider", "timeline"], "ms-python.gather": ["notebookCellExecutionState"], "ms-python.vscode-pylance": [], "ms-python.debugpy": ["contribViewsWelcome", "debugVisualization", "portsAttributes"], "ms-toolsai.jupyter-renderers": ["contribNotebookStaticPreloads"], "ms-toolsai.jupyter": ["notebookDeprecated", "notebookMessaging", "notebookMime", "portsAttributes", "quickPickSortByLabel", "notebookKernelSource", "interactiveWindow", "notebookControllerAffinityHidden", "contribNotebookStaticPreloads", "quickPickItemTooltip", "notebookExecution", "notebookCellExecution", "notebookVariableProvider", "notebookReplDocument"], "ms-toolsai.tensorboard": ["portsAttributes"], "dbaeumer.vscode-eslint": [], "ms-vscode.azure-sphere-tools-ui": ["tunnels"], "ms-azuretools.vscode-azureappservice": ["terminalDataWriteEvent"], "ms-vscode.anycode": ["extensionsAny"], "ms-vscode.cpptools": ["terminalDataWriteEvent", "chatParticipantAdditions"], "vscjava.vscode-java-pack": [], "ms-dotnettools.csdevkit": ["inlineCompletionsAdditions"], "ms-dotnettools.vscodeintellicode-csharp": ["inlineCompletionsAdditions"], "microsoft-IsvExpTools.powerplatform-vscode": ["fileSearchProvider", "textSearchProvider"], "microsoft-IsvExpTools.powerplatform-vscode-preview": ["fileSearchProvider", "textSearchProvider"], "TeamsDevApp.ms-teams-vscode-extension": ["chatParticipantAdditions", "languageModelSystem"], "ms-toolsai.datawrangler": [], "ms-vscode.vscode-commander": [], "ms-autodev.vscode-autodev": ["chatParticipantAdditions"], "jeanp413.open-remote-ssh": ["resolvers", "tunnels", "terminalDataWriteEvent", "contribRemoteHelp", "contribViewsRemote"], "jeanp413.open-remote-wsl": ["resolvers", "contribRemoteHelp", "contribViewsRemote"]}, "extensionKind": {"joycode-l10n": ["ui"], "jdcom.clouddev": ["ui", "workspace"], "Shan.code-settings-sync": ["ui"], "shalldie.background": ["ui"], "techer.open-in-browser": ["ui"], "CoenraadS.bracket-pair-colorizer-2": ["ui"], "CoenraadS.bracket-pair-colorizer": ["ui", "workspace"], "hiro-sun.vscode-emacs": ["ui", "workspace"], "hnw.vscode-auto-open-markdown-preview": ["ui", "workspace"], "wayou.vscode-todo-highlight": ["ui", "workspace"], "aaron-bond.better-comments": ["ui", "workspace"], "vscodevim.vim": ["ui"], "ollyhayes.colmak-vim": ["ui"]}, "extensionPointExtensionKind": {"typescriptServerPlugins": ["workspace"]}, "extensionSyncedKeys": {"ritwickdey.liveserver": ["liveServer.setup.version"]}, "extensionVirtualWorkspacesSupport": {"esbenp.prettier-vscode": {"default": false}, "msjsdiag.debugger-for-chrome": {"default": false}, "redhat.java": {"default": false}, "HookyQR.beautify": {"default": false}, "ritwickdey.LiveServer": {"default": false}, "VisualStudioExptTeam.vscodeintellicode": {"default": false}, "octref.vetur": {"default": false}, "formulahendry.code-runner": {"default": false}, "xdebug.php-debug": {"default": false}, "ms-mssql.mssql": {"default": false}, "christian-kohler.path-intellisense": {"default": false}, "eg2.tslint": {"default": false}, "eg2.vscode-npm-script": {"default": false}, "donjayamanne.githistory": {"default": false}, "Zignd.html-css-class-completion": {"default": false}, "christian-kohler.npm-intellisense": {"default": false}, "EditorConfig.EditorConfig": {"default": false}, "austin.code-gnu-global": {"default": false}, "johnpapa.Angular2": {"default": false}, "ms-vscode.vscode-typescript-tslint-plugin": {"default": false}, "DotJoshJohnson.xml": {"default": false}, "techer.open-in-browser": {"default": false}, "tht13.python": {"default": false}, "bmewburn.vscode-intelephense-client": {"default": false}, "Angular.ng-template": {"default": false}, "xdebug.php-pack": {"default": false}, "dbaeumer.jshint": {"default": false}, "yzhang.markdown-all-in-one": {"default": false}, "Dart-Code.flutter": {"default": false}, "streetsidesoftware.code-spell-checker": {"default": false}, "rebornix.Ruby": {"default": false}, "ms-vscode.sublime-keybindings": {"default": false}, "mitaki28.vscode-clang": {"default": false}, "steoates.autoimport": {"default": false}, "donjayamanne.python-extension-pack": {"default": false}, "shd101wyy.markdown-preview-enhanced": {"default": false}, "mikestead.dotenv": {"default": false}, "pranaygp.vscode-css-peek": {"default": false}, "ikappas.phpcs": {"default": false}, "platformio.platformio-ide": {"default": false}, "jchannon.csharpextensions": {"default": false}, "gruntfuggly.todo-tree": {"default": false}}}